import time
from concurrent import futures
from datetime import datetime

from huma_plugins.components.ai_engine.adapters.ai_platform import AIPlatform
from huma_plugins.components.ai_engine.exceptions import GenAIEngineNotConfiguredException
from huma_plugins.components.ai_engine.models.common import Action, Context
from huma_plugins.components.ai_engine.repository.ai_log_repository import (
    AILogRepository,
)
from huma_plugins.components.ai_engine.service.data_extraction_service import (
    DataExtractionService,
)
from huma_plugins.components.ai_engine.service.prompts import (
    GENERATE_MESSAGE_SYSTEM_PROMPT,
    GENERATE_NOTE_SYSTEM_PROMPT,
    GENERATE_NOTE_USER_PROMPT,
)
from huma_plugins.components.ai_engine.utils.string_utils import to_str
from sdk.authorization.dtos.user import UserDTO
from sdk.common.exceptions.exceptions import (
    InternalServerErrorException,
    InvalidRequestException,
)
from sdk.common.utils import inject
from sdk.common.utils.inject import autoparams
from sdk.deployment.dtos.deployment import DeploymentDTO
from sdk.inbox.dtos.message_dto import MessageDTO
from sdk.phoenix.config.server_config import PhoenixServerConfig


class TextGenAIService:
    NO_DATA_MESSAGE = "No data available"

    @autoparams()
    def __init__(
        self,
        ai_log_repository: AILogRepository,
        data_extraction_service: DataExtractionService,
        server_config: PhoenixServerConfig,
    ):
        self.ai_platform = inject.instance(AIPlatform, safe=True)
        if not self.ai_platform:
            raise GenAIEngineNotConfiguredException()

        self.ai_platform_timeout = server_config.server.aiEngine.aiPlatformTimeout
        self.ai_log_repo = ai_log_repository
        self.data_extraction_service = data_extraction_service

    def generate_text(
        self,
        submitter: UserDTO,
        user: UserDTO,
        deployment: DeploymentDTO,
        action: Action,
        contexts: list[Context],
        input_text: str,
    ) -> tuple[str, str]:
        user_profile = (
            self.data_extraction_service.get_user_profile(user=user) if Context.DEMOGRAPHICS in contexts else None
        )
        staff_profile = self.data_extraction_service.get_staff_profile(user=submitter)

        unseen_module_results, seen_module_results = (
            self.data_extraction_service.get_user_health_data(user=user, deployment=deployment)
            if Context.UNRESOLVED_FLAGS in contexts
            else (None, None)
        )

        if action == Action.GENERATE_MESSAGE and not user_profile and not input_text:
            return "", self.NO_DATA_MESSAGE
        if action == Action.GENERATE_NOTE and not unseen_module_results:
            return "", self.NO_DATA_MESSAGE

        user_medications = self.data_extraction_service.get_user_medications(user=user, deployment_id=deployment.id)

        prompts = self._get_default_prompts(action=action)
        default_prompt = prompts[0]

        prompts[0] = self._get_prompt(
            default_prompt=default_prompt,
            action=action,
            user_profile=user_profile,
            staff_profile=staff_profile,
            unseen_module_results=unseen_module_results,
            seen_module_results=seen_module_results,
            user_medications=user_medications,
            input_text=input_text,
        )
        start_time = time.time()
        with futures.ThreadPoolExecutor() as executor:
            future = executor.submit(self.ai_platform.generate_text, prompts)
            try:
                result = future.result(self.ai_platform_timeout)
            except futures.TimeoutError:
                raise InternalServerErrorException("AI Platform timed out")
            except Exception:
                raise InternalServerErrorException
        generated_text, response = result
        end_time = time.time()
        text_generation_time = int(end_time - start_time)
        if action == Action.GENERATE_MESSAGE and len(generated_text) > MessageDTO.MAX_MESSAGE_LENGTH:
            raise InternalServerErrorException("Generated message is too long")

        full_prompt = "\n".join(prompts)
        self.ai_log_repo.add_generated_text_log(
            platform=self.ai_platform.name,
            model_id=self.ai_platform.model_id,
            input=full_prompt,
            response=response,
            output=generated_text,
            user_id=user.id,
            submitter_id=submitter.id,
            response_time=text_generation_time,
        )
        return full_prompt, generated_text

    def _get_prompt(
        self,
        default_prompt: str,
        action: Action,
        user_profile: dict,
        staff_profile: dict,
        unseen_module_results: list[dict],
        seen_module_results: list[dict],
        user_medications: list[dict],
        input_text: str,
    ) -> str:
        if action == Action.GENERATE_NOTE:
            return self._get_prompt_for_generate_note(
                default_prompt=default_prompt,
                user_profile=user_profile,
                unseen_module_results=unseen_module_results,
                seen_module_results=seen_module_results,
                user_medications=user_medications,
                input_text=input_text,
            )

        elif action == Action.GENERATE_MESSAGE:
            return self._get_prompt_for_generate_message(
                default_prompt=default_prompt,
                user_profile=user_profile,
                staff_profile=staff_profile,
                input_text=input_text,
            )
        elif action == Action.GENERATE_REPORT:
            raise NotImplementedError
        else:
            raise InvalidRequestException(f"Invalid action: {action}")

    @staticmethod
    def _get_prompt_for_generate_note(
        default_prompt: str,
        user_profile: dict,
        unseen_module_results: list[dict],
        seen_module_results: list[dict],
        user_medications: list[dict],
        input_text: str,
    ) -> str:
        prompt = default_prompt
        if user_profile:
            prompt += f"\n\n PATIENT'S DEMOGRAPHICS: {to_str(user_profile)}"
        if unseen_module_results:
            prompt += f"\n\n RECENT HEALTH DATA: {to_str(unseen_module_results)}"
        if seen_module_results:
            prompt += f"\n\n HEALTH DATA: {to_str(seen_module_results)}"
        if user_medications:
            prompt += f"\n\n PATIENT'S MEDICATIONS: {to_str(user_medications)}"
        prompt += f"\n\n CURRENT DATE: {datetime.utcnow().strftime('%Y-%m-%d')}"
        return prompt

    @staticmethod
    def _get_prompt_for_generate_message(
        default_prompt: str,
        user_profile: dict,
        staff_profile: dict,
        input_text: str,
    ) -> str:
        prompt = default_prompt
        if user_profile:
            prompt += f"\n\n PATIENT'S DEMOGRAPHICS: {to_str(user_profile)}"
        if staff_profile and staff_profile.get("name"):
            prompt += f"\n\n CLINICIAN'S NAME: {staff_profile['name']}"
        if input_text:
            prompt += f"\n\n MESSAGE INTENT: {input_text}"
        return prompt

    def _get_default_prompts(self, action: Action) -> list[str]:
        if action == Action.GENERATE_NOTE:
            return [GENERATE_NOTE_SYSTEM_PROMPT, GENERATE_NOTE_USER_PROMPT]
        elif action == Action.GENERATE_MESSAGE:
            return [GENERATE_MESSAGE_SYSTEM_PROMPT]
        elif action == Action.GENERATE_REPORT:
            raise NotImplementedError
        else:
            raise InvalidRequestException(f"Invalid action: {action}")
